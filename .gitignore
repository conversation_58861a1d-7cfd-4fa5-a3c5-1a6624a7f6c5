# 依赖
node_modules/
.pnp
.pnp.js

# 构建输出
dist/
build/
*.tsbuildinfo

# 环境变量文件 - 敏感信息
.env.local
.env.*.local

# 开发工具
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
*.lcov
.nyc_output

# 依赖管理
.npm
.eslintcache
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# 微信开发工具
.wechat_devtools/

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# 编辑器和IDE
*.sublime-project
*.sublime-workspace
.vscode/
.idea/
*.iml
*.ipr
*.iws

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 测试文件
.jest/
test-results/
playwright-report/
test-results/

# Storybook
storybook-static/

# Bundle 分析
stats.html
bundle-analyzer-report.html

# 生产环境调试文件
*.map